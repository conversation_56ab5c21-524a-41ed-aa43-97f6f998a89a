"""
测试 RAG 服务的 retrieve 方法实现
"""
import unittest
from unittest.mock import AsyncM<PERSON>, Mock, patch
from core.rag.rag_service import RAGServiceBase
from core.services.database.crud.user_uploaded_files import user_uploaded_files_crud


class MockRAGService(RAGServiceBase):
    """用于测试的 RAG 服务模拟实现"""
    
    async def upload_file_to_rag_service(self, **kwargs):
        pass
    
    async def parse_file(self, file_id: str):
        pass
    
    async def get_file_status(self, file_id: str) -> dict:
        return {}
    
    async def delete_file_from_rag_service(self, user_id: str, file_ids: list[str]) -> bool:
        return True
    
    async def retrieve_from_documents(self, query: str, user_id: str, file_ids: list[str]) -> list[dict]:
        """模拟文档检索"""
        return [
            {
                "content": f"Test content for query: {query}",
                "similarity": 0.85,
                "document_id": file_ids[0] if file_ids else "test-doc-id",
                "chunk_id": "chunk-1",
                "metadata": {"page": 1}
            }
        ]


class TestRAGServiceRetrieve(unittest.IsolatedAsyncioTestCase):
    """测试 RAG 服务的 retrieve 方法"""
    
    def setUp(self):
        """设置测试环境"""
        self.service = MockRAGService("test-rag")
        self.test_user_id = "123"
        self.test_query = "test query"
        self.test_user_file_ids = ["user-file-1", "user-file-2"]
        self.test_rag_file_ids = ["rag-file-1", "rag-file-2"]
    
    @patch('core.rag.rag_service.db_manager')
    @patch('core.rag.rag_service.user_uploaded_files_crud')
    async def test_retrieve_success(self, mock_crud, mock_db_manager):
        """测试成功检索文档"""
        # 模拟数据库会话
        mock_session = AsyncMock()
        mock_db_manager.session.return_value.__aenter__.return_value = mock_session
        
        # 模拟 CRUD 操作返回 RAG 文件 IDs (使用 AsyncMock)
        mock_crud.get_rag_file_ids_by_user_file_ids = AsyncMock(return_value=self.test_rag_file_ids)
        
        # 执行检索
        result = await self.service.retrieve(
            query=self.test_query,
            ids=self.test_user_file_ids,
            user_id=self.test_user_id
        )
        
        # 验证结果
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 1)
        self.assertIn("content", result[0])
        self.assertIn("similarity", result[0])
        self.assertIn("document_id", result[0])
        
        # 验证 CRUD 方法被正确调用
        mock_crud.get_rag_file_ids_by_user_file_ids.assert_called_once_with(
            mock_session,
            user_file_ids=self.test_user_file_ids,
            user_id=int(self.test_user_id)
        )
    
    @patch('core.rag.rag_service.db_manager')
    @patch('core.rag.rag_service.user_uploaded_files_crud')
    async def test_retrieve_file_not_found(self, mock_crud, mock_db_manager):
        """测试文件未找到或无权限访问的情况"""
        # 模拟数据库会话
        mock_session = AsyncMock()
        mock_db_manager.session.return_value.__aenter__.return_value = mock_session
        
        # 模拟 CRUD 操作返回部分文件（权限验证失败）
        mock_crud.get_rag_file_ids_by_user_file_ids = AsyncMock(return_value=["rag-file-1"])  # 只返回一个文件
        
        # 执行检索，应该抛出异常
        with self.assertRaises(ValueError) as context:
            await self.service.retrieve(
                query=self.test_query,
                ids=self.test_user_file_ids,  # 请求两个文件
                user_id=self.test_user_id
            )
        
        self.assertIn("Some files not found or access denied", str(context.exception))
    
    @patch('core.rag.rag_service.db_manager')
    @patch('core.rag.rag_service.user_uploaded_files_crud')
    async def test_retrieve_empty_file_list(self, mock_crud, mock_db_manager):
        """测试空文件列表的情况"""
        # 模拟数据库会话
        mock_session = AsyncMock()
        mock_db_manager.session.return_value.__aenter__.return_value = mock_session
        
        # 模拟 CRUD 操作返回空列表
        mock_crud.get_rag_file_ids_by_user_file_ids = AsyncMock(return_value=[])
        
        # 执行检索，应该抛出异常
        with self.assertRaises(ValueError) as context:
            await self.service.retrieve(
                query=self.test_query,
                ids=["non-existent-file"],
                user_id=self.test_user_id
            )
        
        self.assertIn("Some files not found or access denied", str(context.exception))
    
    @patch('core.rag.rag_service.db_manager')
    @patch('core.rag.rag_service.user_uploaded_files_crud')
    async def test_retrieve_database_error(self, mock_crud, mock_db_manager):
        """测试数据库错误的情况"""
        # 模拟数据库会话
        mock_session = AsyncMock()
        mock_db_manager.session.return_value.__aenter__.return_value = mock_session
        
        # 模拟 CRUD 操作抛出异常
        mock_crud.get_rag_file_ids_by_user_file_ids = AsyncMock(side_effect=Exception("Database error"))
        
        # 执行检索，应该抛出异常
        with self.assertRaises(Exception) as context:
            await self.service.retrieve(
                query=self.test_query,
                ids=self.test_user_file_ids,
                user_id=self.test_user_id
            )
        
        self.assertIn("Database error", str(context.exception))


def main():
    """运行测试"""
    print("🧪 Testing RAG Service retrieve method implementation...")

    # 创建测试套件
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestRAGServiceRetrieve)

    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    print("\n✅ RAG Service retrieve method tests completed!")
    print(f"   - Tests run: {result.testsRun}")
    print(f"   - Failures: {len(result.failures)}")
    print(f"   - Errors: {len(result.errors)}")

    print("\n📋 Implementation Summary:")
    print("   - ✅ retrieve method validates file ownership")
    print("   - ✅ retrieve method converts user file IDs to RAG file IDs")
    print("   - ✅ retrieve method calls retrieve_from_documents")
    print("   - ✅ retrieve method handles errors appropriately")

    return result.wasSuccessful()


if __name__ == '__main__':
    main()
