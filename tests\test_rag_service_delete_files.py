"""
测试 RAG 服务的 delete_files 方法实现
"""
import unittest
from unittest.mock import Async<PERSON><PERSON>, Mock, patch
from core.rag.rag_service import RAGServiceBase
from core.services.database.crud.user_uploaded_files import user_uploaded_files_crud


class MockRAGService(RAGServiceBase):
    """用于测试的 RAG 服务模拟实现"""
    
    def __init__(self, rag_server: str, delete_rag_success: bool = True):
        super().__init__(rag_server)
        self.delete_rag_success = delete_rag_success
    
    async def upload_file_to_rag_service(self, **kwargs):
        pass
    
    async def parse_file(self, file_id: str):
        pass
    
    async def get_file_status(self, file_id: str) -> dict:
        return {}
    
    async def delete_file_from_rag_service(self, user_id: str, file_ids: list[str]) -> bool:
        """模拟 RAG 服务删除文件"""
        return self.delete_rag_success
    
    async def retrieve_from_documents(self, query: str, user_id: str, file_ids: list[str]) -> list[dict]:
        return []


class TestRAGServiceDeleteFiles(unittest.IsolatedAsyncioTestCase):
    """测试 RAG 服务的 delete_files 方法"""
    
    def setUp(self):
        """设置测试环境"""
        self.service = MockRAGService("test-rag")
        self.test_user_id = "123"
        self.test_user_file_ids = ["user-file-1", "user-file-2"]
        self.test_rag_file_ids = ["rag-file-1", "rag-file-2"]
    
    async def test_delete_files_empty_list(self):
        """测试删除空文件列表"""
        result = await self.service.delete_files([], self.test_user_id)
        self.assertTrue(result)
    
    @patch('core.rag.rag_service.db_manager')
    @patch('core.rag.rag_service.user_uploaded_files_crud')
    async def test_delete_files_success(self, mock_crud, mock_db_manager):
        """测试成功删除文件"""
        # 模拟数据库会话
        mock_session = AsyncMock()
        mock_db_manager.session.return_value.__aenter__.return_value = mock_session
        
        # 模拟 CRUD 操作返回 RAG 文件 IDs
        mock_crud.get_rag_file_ids_by_user_file_ids = AsyncMock(return_value=self.test_rag_file_ids)
        mock_crud.delete_by_rag_file_id = AsyncMock(return_value=True)
        
        # 执行删除
        result = await self.service.delete_files(
            ids=self.test_user_file_ids,
            user_id=self.test_user_id
        )
        
        # 验证结果
        self.assertTrue(result)
        
        # 验证 CRUD 方法被正确调用
        mock_crud.get_rag_file_ids_by_user_file_ids.assert_called_once_with(
            mock_session,
            user_file_ids=self.test_user_file_ids,
            user_id=int(self.test_user_id)
        )
        
        # 验证每个文件都被删除
        self.assertEqual(mock_crud.delete_by_rag_file_id.call_count, len(self.test_rag_file_ids))
    
    @patch('core.rag.rag_service.db_manager')
    @patch('core.rag.rag_service.user_uploaded_files_crud')
    async def test_delete_files_file_not_found(self, mock_crud, mock_db_manager):
        """测试文件未找到或无权限访问的情况"""
        # 模拟数据库会话
        mock_session = AsyncMock()
        mock_db_manager.session.return_value.__aenter__.return_value = mock_session
        
        # 模拟 CRUD 操作返回部分文件（权限验证失败）
        mock_crud.get_rag_file_ids_by_user_file_ids = AsyncMock(return_value=["rag-file-1"])  # 只返回一个文件
        
        # 执行删除，应该返回 False
        result = await self.service.delete_files(
            ids=self.test_user_file_ids,  # 请求两个文件
            user_id=self.test_user_id
        )
        
        self.assertFalse(result)
    
    @patch('core.rag.rag_service.db_manager')
    @patch('core.rag.rag_service.user_uploaded_files_crud')
    async def test_delete_files_rag_service_failure(self, mock_crud, mock_db_manager):
        """测试 RAG 服务删除失败但数据库删除成功的情况"""
        # 创建一个 RAG 服务删除失败的服务实例
        service = MockRAGService("test-rag", delete_rag_success=False)
        
        # 模拟数据库会话
        mock_session = AsyncMock()
        mock_db_manager.session.return_value.__aenter__.return_value = mock_session
        
        # 模拟 CRUD 操作
        mock_crud.get_rag_file_ids_by_user_file_ids = AsyncMock(return_value=self.test_rag_file_ids)
        mock_crud.delete_by_rag_file_id = AsyncMock(return_value=True)
        
        # 执行删除
        result = await service.delete_files(
            ids=self.test_user_file_ids,
            user_id=self.test_user_id
        )
        
        # 即使 RAG 服务删除失败，如果数据库删除成功，整体操作仍然成功
        self.assertTrue(result)
    
    @patch('core.rag.rag_service.db_manager')
    @patch('core.rag.rag_service.user_uploaded_files_crud')
    async def test_delete_files_database_deletion_failure(self, mock_crud, mock_db_manager):
        """测试数据库删除失败的情况"""
        # 模拟数据库会话
        mock_session = AsyncMock()
        mock_db_manager.session.return_value.__aenter__.return_value = mock_session
        
        # 模拟 CRUD 操作
        mock_crud.get_rag_file_ids_by_user_file_ids = AsyncMock(return_value=self.test_rag_file_ids)
        mock_crud.delete_by_rag_file_id = AsyncMock(return_value=False)  # 数据库删除失败
        
        # 执行删除
        result = await self.service.delete_files(
            ids=self.test_user_file_ids,
            user_id=self.test_user_id
        )
        
        # 数据库删除失败，整体操作失败
        self.assertFalse(result)
    
    @patch('core.rag.rag_service.db_manager')
    @patch('core.rag.rag_service.user_uploaded_files_crud')
    async def test_delete_files_partial_database_failure(self, mock_crud, mock_db_manager):
        """测试部分数据库删除失败的情况"""
        # 模拟数据库会话
        mock_session = AsyncMock()
        mock_db_manager.session.return_value.__aenter__.return_value = mock_session
        
        # 模拟 CRUD 操作
        mock_crud.get_rag_file_ids_by_user_file_ids = AsyncMock(return_value=self.test_rag_file_ids)
        # 模拟第一个文件删除成功，第二个失败
        mock_crud.delete_by_rag_file_id = AsyncMock(side_effect=[True, False])
        
        # 执行删除
        result = await self.service.delete_files(
            ids=self.test_user_file_ids,
            user_id=self.test_user_id
        )
        
        # 部分删除失败，整体操作失败
        self.assertFalse(result)
    
    @patch('core.rag.rag_service.db_manager')
    @patch('core.rag.rag_service.user_uploaded_files_crud')
    async def test_delete_files_database_exception(self, mock_crud, mock_db_manager):
        """测试数据库操作异常的情况"""
        # 模拟数据库会话
        mock_session = AsyncMock()
        mock_db_manager.session.return_value.__aenter__.return_value = mock_session
        
        # 模拟 CRUD 操作抛出异常
        mock_crud.get_rag_file_ids_by_user_file_ids = AsyncMock(side_effect=Exception("Database error"))
        
        # 执行删除，应该返回 False
        result = await self.service.delete_files(
            ids=self.test_user_file_ids,
            user_id=self.test_user_id
        )
        
        self.assertFalse(result)


def main():
    """运行测试"""
    print("🧪 Testing RAG Service delete_files method implementation...")

    # 创建测试套件
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestRAGServiceDeleteFiles)

    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    print("\n✅ RAG Service delete_files method tests completed!")
    print(f"   - Tests run: {result.testsRun}")
    print(f"   - Failures: {len(result.failures)}")
    print(f"   - Errors: {len(result.errors)}")

    print("\n📋 Implementation Summary:")
    print("   - ✅ delete_files method validates file ownership")
    print("   - ✅ delete_files method converts user file IDs to RAG file IDs")
    print("   - ✅ delete_files method calls delete_file_from_rag_service")
    print("   - ✅ delete_files method deletes database records")
    print("   - ✅ delete_files method handles errors appropriately")
    print("   - ✅ delete_files method handles empty file lists")

    return result.wasSuccessful()


if __name__ == '__main__':
    main()
