"""
RAG Service retrieve 方法使用示例

这个示例展示了如何使用 RAG 服务的 retrieve 方法来检索文档内容。
"""
import asyncio
from typing import List
from core.rag.ragflow import RagFlowRAGService
from core.config.app_config import app_config


async def example_retrieve_documents():
    """
    演示如何使用 retrieve 方法检索文档内容
    """
    print("🚀 RAG Service retrieve 方法使用示例")
    print("=" * 50)
    
    # 初始化 RAG 服务
    try:
        rag_service = RagFlowRAGService()
        print("✅ RAG 服务初始化成功")
    except Exception as e:
        print(f"❌ RAG 服务初始化失败: {e}")
        return
    
    # 示例数据
    user_id = "123"  # 用户ID
    query = "什么是人工智能？"  # 查询问题
    user_file_ids = [  # 用户上传的文件ID列表（来自 user_uploaded_files 表）
        "user-file-id-1",
        "user-file-id-2",
        "user-file-id-3"
    ]
    
    print(f"\n📋 检索参数:")
    print(f"   - 用户ID: {user_id}")
    print(f"   - 查询内容: {query}")
    print(f"   - 文件ID列表: {user_file_ids}")
    
    try:
        # 调用 retrieve 方法
        print(f"\n🔍 开始检索文档...")
        results = await rag_service.retrieve(
            query=query,
            ids=user_file_ids,
            user_id=user_id
        )
        
        print(f"✅ 检索成功，找到 {len(results)} 个相关文档块")
        
        # 显示检索结果
        for i, result in enumerate(results, 1):
            print(f"\n📄 结果 {i}:")
            print(f"   - 内容: {result.get('content', '')[:100]}...")
            print(f"   - 相似度: {result.get('similarity', 0):.3f}")
            print(f"   - 文档ID: {result.get('document_id', '')}")
            print(f"   - 块ID: {result.get('chunk_id', '')}")
            
            # 显示元数据（如果有）
            metadata = result.get('metadata', {})
            if metadata:
                print(f"   - 元数据: {metadata}")
        
    except ValueError as e:
        print(f"❌ 权限验证失败: {e}")
        print("   可能的原因:")
        print("   - 某些文件不存在")
        print("   - 某些文件不属于当前用户")
        print("   - 文件ID格式不正确")
        
    except Exception as e:
        print(f"❌ 检索失败: {e}")
        print("   可能的原因:")
        print("   - 数据库连接问题")
        print("   - RAG 服务不可用")
        print("   - 网络连接问题")


async def example_retrieve_workflow():
    """
    演示完整的文档检索工作流程
    """
    print("\n" + "=" * 50)
    print("🔄 完整的文档检索工作流程示例")
    print("=" * 50)
    
    # 这个示例展示了在实际应用中如何使用 retrieve 方法
    
    # 步骤1: 用户上传文件（这里假设已经完成）
    print("📤 步骤1: 用户上传文件（假设已完成）")
    uploaded_file_ids = ["file-1", "file-2", "file-3"]
    print(f"   - 已上传文件: {uploaded_file_ids}")
    
    # 步骤2: 用户提出问题
    print("\n❓ 步骤2: 用户提出问题")
    user_question = "请总结这些文档的主要内容"
    print(f"   - 用户问题: {user_question}")
    
    # 步骤3: 使用 retrieve 方法检索相关内容
    print("\n🔍 步骤3: 检索相关文档内容")
    try:
        rag_service = RagFlowRAGService()
        
        # 调用 retrieve 方法
        relevant_chunks = await rag_service.retrieve(
            query=user_question,
            ids=uploaded_file_ids,
            user_id="123"
        )
        
        print(f"   - 找到 {len(relevant_chunks)} 个相关文档块")
        
        # 步骤4: 基于检索结果生成回答（这里只是示例）
        print("\n💡 步骤4: 基于检索结果生成回答")
        if relevant_chunks:
            print("   - 可以将这些文档块作为上下文传递给大语言模型")
            print("   - 生成基于文档内容的准确回答")
        else:
            print("   - 没有找到相关内容，可能需要调整查询策略")
            
    except Exception as e:
        print(f"   ❌ 工作流程执行失败: {e}")


def print_usage_notes():
    """
    打印使用说明
    """
    print("\n" + "=" * 50)
    print("📝 使用说明")
    print("=" * 50)
    
    print("\n🔧 方法签名:")
    print("   async def retrieve(self, query: str, ids: List[str], user_id: str) -> list[dict]")
    
    print("\n📥 参数说明:")
    print("   - query: 查询字符串，用于检索相关文档内容")
    print("   - ids: 用户文件ID列表（user_uploaded_files表的id字段）")
    print("   - user_id: 用户ID，用于权限验证")
    
    print("\n📤 返回值:")
    print("   - list[dict]: 检索结果列表，每个元素包含:")
    print("     * content: 文档块内容")
    print("     * similarity: 相似度分数")
    print("     * document_id: 文档ID")
    print("     * chunk_id: 文档块ID")
    print("     * metadata: 元数据信息")
    
    print("\n⚠️  注意事项:")
    print("   1. 方法会自动验证文件所有权，确保用户只能访问自己的文件")
    print("   2. 如果某些文件不存在或无权限访问，会抛出 ValueError")
    print("   3. 方法内部会将用户文件ID转换为RAG文件ID")
    print("   4. 实际的文档检索由 retrieve_from_documents 方法执行")
    
    print("\n🔗 相关方法:")
    print("   - upload_file(): 上传文件到RAG服务")
    print("   - delete_files(): 删除文件")
    print("   - get_file_status(): 查询文件状态")


async def main():
    """
    主函数，运行所有示例
    """
    await example_retrieve_documents()
    await example_retrieve_workflow()
    print_usage_notes()
    
    print("\n🎉 示例运行完成！")
    print("\n💡 提示:")
    print("   - 在实际使用中，请确保RAG服务已正确配置")
    print("   - 确保数据库连接正常")
    print("   - 确保用户文件已成功上传并解析完成")


if __name__ == "__main__":
    asyncio.run(main())
